# 技术文档

## 数据模型

应用的数据模型定义在 [models.py](mdc:models.py) 文件中。

### 主要模型

#### Post 模型

微博内容的数据模型：

```python
class Post(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    content = db.Column(db.Text, nullable=False)
    tags = db.Column(JSON, default=list)
    created_at = db.Column(db.DateTime, nullable=False, default=beijing_time)
```

- `id`: 主键
- `content`: 微博内容
- `tags`: JSON类型字段，存储标签列表
- `created_at`: 创建时间，默认为北京时间

#### User 模型

用户数据模型：

```python
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
```

- `id`: 主键
- `username`: 用户名
- `password_hash`: 密码的哈希值

### 辅助函数

- `beijing_time()`: 返回带北京时区调整的当前时间

## 路由和API端点

应用的所有路由和API端点都定义在 [app.py](mdc:app.py) 中。

### 主要页面路由

- `@app.route('/')` - 主页，显示微博列表，支持分页、标签过滤和关键词搜索
- `@app.route('/login')` - 用户登录页面
- `@app.route('/logout')` - 用户登出
- `@app.route('/random')` - 随机浏览微博页面
- `@app.route('/tags')` - 标签页面，显示所有标签及其使用次数

### 微博操作路由

- `@app.route('/post/create', methods=['POST'])` - 创建新微博
- `@app.route('/post/<int:post_id>/update', methods=['POST'])` - 更新微博
- `@app.route('/post/<int:post_id>/delete', methods=['POST'])` - 删除微博

### API端点

- `@app.route('/api/random_post')` - 获取随机微博的API
- `@app.route('/api/post/navigate')` - 导航上一条/下一条微博的API

### 文件上传

- `@app.route('/upload', methods=['POST'])` - 文件上传处理

### 身份验证

应用使用Flask的session机制管理用户登录状态，大多数路由都使用`@login_required`装饰器来保护，确保只有登录用户才能访问。

## 前端模板

应用使用Flask的Jinja2模板引擎渲染HTML页面。主要模板文件位于 [templates/](mdc:templates) 目录。

### 主要模板文件

- [templates/index.html](mdc:templates/index.html) - 主页模板，显示微博列表，包含发布微博的表单和微博展示区域
- [templates/login.html](mdc:templates/login.html) - 登录页面模板
- [templates/random.html](mdc:templates/random.html) - 随机浏览微博页面模板
- [templates/tags.html](mdc:templates/tags.html) - 标签页面模板，展示所有标签及其使用次数
- [templates/_edit_modal.html](mdc:templates/_edit_modal.html) - 编辑微博的模态窗口模板，被其他模板引用

### 静态资源

静态资源文件位于 [static/](mdc:static) 目录，包括：

- CSS样式文件
- JavaScript脚本
- 图标和其他媒体文件

### JavaScript功能

主要前端交互功能包括：

- 添加和删除标签
- AJAX微博发布和编辑
- 随机浏览微博的导航
- 文件上传预览和处理
