## 简要概述
该规则文件规定了每次代码修改后必须更新 CHANGELOG.md 文件的要求。变更日志遵循 Keep a Changelog 格式，包含未发布版本和已发布版本章节。

## 变更日志更新要求
- 每次修改代码后必须同步更新 CHANGELOG.md 文件
- 更新应在代码修改完成且测试通过后进行
- 变更内容应归类到适当的类别下（功能、修复、改进）
- 对于未发布版本的变更，应添加到 [Unreleased] 版本下

## 变更日志格式规范
- 使用标准的 Keep a Changelog 格式
- 每个版本下分为功能、修复、改进三个类别
- 新增内容应使用项目符号列表格式
- 变更日期格式使用 YYYY-MM-DD

## 更新流程
1. 确定变更类型（功能新增、Bug修复、代码改进）
2. 在 CHANGELOG.md 的 [Unreleased] 部分找到对应类别
3. 添加简洁明了的变更描述
4. 确保描述准确反映代码变更内容
5. 提交变更时将 changelog 更新包含在同一个提交中

## 示例
```
## [Unreleased]

### 功能
- 添加用户登录功能

### 修复
- 修复标签显示错误的问题

### 改进
- 优化数据库查询性能
