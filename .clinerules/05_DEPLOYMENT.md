## 服务器启动配置

此规则文件定义了微博应用的Docker容器化部署配置和运行方式。

## Docker容器化部署

### 容器配置
- **基础镜像**: Python 3.9-slim
- **工作目录**: /app
- **环境变量**:
  - `PYTHONDONTWRITEBYTECODE=1` - 禁止生成.pyc文件
  - `PYTHONUNBUFFERED=1` - 无缓冲输出
  - `FLASK_APP=app.py` - Flask应用入口
  - `PORT=5001` - 服务监听端口
  - `FLASK_ENV=production` - 生产环境模式

### 生产服务器
- **Web服务器**: gunicorn
- **绑定地址**: 0.0.0.0:5001
- **工作进程数**: 4
- **健康检查**: 每30秒检查一次应用状态

### Docker Compose配置
- **服务名称**: weibo-app
- **端口映射**: 5001:5001
- **数据卷**:
  - weibo-uploads-data → /app/uploads (上传文件存储)
  - ./weibo.db → /app/weibo.db (数据库文件)
- **重启策略**: unless-stopped (除非手动停止，否则自动重启)
- **网络**: 使用专用桥接网络

## 部署方式

### 本地Docker部署
```bash
# 完整部署流程
./scripts/docker-deploy.sh deploy

# 仅构建镜像
./scripts/docker-deploy.sh build

# 启动服务
./scripts/docker-deploy.sh up

# 查看服务状态
./scripts/docker-deploy.sh status
```

### 远程服务器部署
```bash
# 部署到远程服务器
./scripts/docker-deploy.sh deploy-remote
```

### 服务管理
```bash
# 停止服务
./scripts/docker-deploy.sh down

# 重启服务
./scripts/docker-deploy.sh restart

# 查看实时日志
./scripts/docker-deploy.sh logs
```

## 开发环境运行

对于本地开发，仍然可以使用Flask内置服务器：

```bash
# 使用开发脚本启动
./scripts/dev-deploy.sh

# 或手动启动
python app.py --port 5001

# 使用环境变量指定端口
PORT=5001 python app.py
```

## 数据库配置
- **数据库类型**: SQLite
- **数据库文件**: weibo.db (位于应用根目录)
- **数据持久化**: 通过Docker卷挂载确保数据持久化

## 会话配置
- 会话密钥已配置
- Session cookie 名称包含端口信息以支持多实例运行
- 最大上传大小限制：100MB

## 环境要求
- **Docker**: 需要安装Docker和docker-compose
- **磁盘空间**: 确保有足够的空间存储镜像和卷数据
- **网络**: 需要网络访问以下载基础镜像和依赖

## 健康监控
容器包含健康检查配置，确保应用服务正常运行：
- 检查间隔: 30秒
- 超时时间: 30秒
- 启动等待: 5秒
- 重试次数: 3次

## 注意事项
1. 首次部署时需要构建镜像，后续部署会使用缓存加速
2. 数据库文件通过卷挂载，确保数据持久化
3. 上传文件存储在Docker数据卷中，重启容器不会丢失
4. 生产环境建议使用反向代理（如Caddy）提供HTTPS支持
