## 简要概述
本规则文件整合了HaoNoteWeb Flask项目的运行和部署规范，确保开发团队遵循统一的操作流程。该规则涵盖了本地开发、测试、部署到生产环境的完整操作指南。

## 项目运行规范

### 本地开发环境
- 使用 `./scripts/dev-deploy.sh` 脚本启动开发环境
- 该脚本会自动检查Docker环境、清理端口占用、构建开发镜像并启动服务
- 开发环境默认在 http://localhost:5001 运行
- 支持热重载，代码修改后容器会自动更新

### 开发脚本使用
- 脚本位于 `scripts/dev-deploy.sh`，需在项目根目录执行
- 执行前确保脚本有执行权限：`chmod +x scripts/dev-deploy.sh`
- 支持多种操作模式：
  - `./scripts/dev-deploy.sh` - 启动开发服务（默认）
  - `./scripts/dev-deploy.sh logs` - 查看实时日志
  - `./scripts/dev-deploy.sh stop` - 停止开发服务
  - `./scripts/dev-deploy.sh restart` - 重启开发服务

### 开发环境特点
- 使用Docker容器化运行，确保环境一致性
- 开启Flask调试模式，便于开发调试
- 数据库文件和上传目录通过卷挂载实现数据持久化
- 支持断点续传和实时日志查看

## 项目部署规范

### 远程服务器部署
- 使用 `./scripts/deploy.sh` 脚本进行远程部署
- 该脚本会自动同步代码到远程服务器并执行部署
- 远程部署默认连接服务器：**************，用户：lighthouse
- 支持一键部署，无需手动SSH操作

### 部署脚本使用
- `./scripts/deploy.sh` - 直接执行远程部署（默认行为）
- `./scripts/deploy.sh deploy` - 明确指定执行部署
- `./scripts/docker-deploy.sh deploy` - 本地生产环境部署
- `./scripts/docker-deploy.sh deploy-remote` - 远程部署（被deploy.sh封装）

## 操作流程规范

### 开发到部署完整流程
1. 本地开发：使用 `./scripts/dev-deploy.sh` 启动开发环境
2. 功能测试：在开发环境中验证功能
3. 本地部署测试：使用 `./scripts/docker-deploy.sh deploy` 测试生产部署
4. 远程部署：使用 `./scripts/deploy.sh` 部署到生产服务器

### 数据库管理
- 数据库初始化：首次部署时自动执行
- 数据库备份：定期运行 `python scripts/backup.py` 进行备份
- 数据库文件权限：确保weibo.db文件权限为644，上传目录权限为755

### 服务器环境要求
- 远程服务器需安装Docker和docker-compose
- 需要配置SSH密钥实现无密码登录
- 服务器需开放5001端口用于应用访问
- 服务器应配置反向代理（如Caddy）支持HTTPS

## 版本管理和变更日志
- 每次代码修改后必须更新 CHANGELOG.md 文件
- 遵循Keep a Changelog格式规范
- 变更内容按功能、修复、改进分类记录
- 未发布版本的变更记录在[Unreleased]版本下

## 备份和恢复策略
- 定期备份：使用 `python scripts/backup.py` 脚本备份数据库
- 备份位置：~/Documents/Backup目录
- 紧急恢复：从备份文件恢复数据库到指定时间点
- 文件同步：使用rsync确保备份文件安全传输
