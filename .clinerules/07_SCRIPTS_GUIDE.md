# 项目脚本使用指南

本项目的常用脚本都位于 [scripts](mdc:scripts) 目录中，以下是各脚本的详细说明：

## 🚀 应用运行脚本

### [scripts/dev-deploy.sh](mdc:scripts/dev-deploy.sh)
智能开发环境部署脚本，功能包括：
- 自动检测Docker服务状态
- 智能决策启动或重启开发服务
- 支持开发环境的热加载功能
- 提供完整的服务管理（启动、停止、重启、查看日志）

使用方法：
```bash
# 智能部署（自动检测状态并执行相应操作）
./scripts/dev-deploy.sh

# 查看帮助信息
./scripts/dev-deploy.sh help

# 启动开发服务
./scripts/dev-deploy.sh start

# 停止开发服务
./scripts/dev-deploy.sh stop

# 重启开发服务
./scripts/dev-deploy.sh restart

# 查看实时日志
./scripts/dev-deploy.sh logs

# 查看服务状态
./scripts/dev-deploy.sh status
```

## 🗄️ 数据库相关脚本

### [scripts/init_db.py](mdc:scripts/init_db.py)
数据库初始化脚本，用于创建数据库表结构
```bash
python scripts/init_db.py
```

### [scripts/backup.py](mdc:scripts/backup.py)
数据库备份脚本，将weibo.db备份到~/Documents/Backup目录
```bash
python scripts/backup.py
```

## 👥 用户管理脚本

### [scripts/create_test_user.py](mdc:scripts/create_test_user.py)
测试用户创建脚本，用于创建默认的测试用户
```bash
python scripts/create_test_user.py
```

## 🚚 部署相关脚本

### [scripts/docker-deploy.sh](mdc:scripts/docker-deploy.sh)
Docker容器化部署脚本，功能包括：
- 本地Docker镜像构建和部署
- 远程服务器Docker部署
- Docker服务管理（启动、停止、重启、状态查看）
- 数据库初始化

使用方法：
```bash
# 本地完整部署
./scripts/docker-deploy.sh deploy

# 部署到远程服务器
./scripts/docker-deploy.sh deploy-remote

# 查看服务状态
./scripts/docker-deploy.sh status

# 查看实时日志
./scripts/docker-deploy.sh logs
```

### [docker-compose.yml](mdc:docker-compose.yml)
Docker Compose配置文件，定义服务、网络和数据卷配置。

### [Dockerfile](mdc:Dockerfile)
Docker镜像构建文件，定义应用运行环境和启动命令。

## 🔧 开发工具脚本

### [scripts/amend.sh](mdc:scripts/amend.sh)
Git快捷操作脚本，执行以下操作：
- 查看当前状态
- 添加所有变更
- 修正最后一次提交
- 显示操作后状态

使用方法：
```bash
./scripts/amend.sh
```

### [scripts/random.py](mdc:scripts/random.py)
随机微博查看脚本，功能包括：
- 从远程服务器同步数据库
- 随机获取一条微博记录
- 使用macOS系统对话框显示内容

使用方法：
```bash
python scripts/random.py
```

## 📝 使用建议

1. **本地开发**: 使用 `./scripts/dev-deploy.sh` 启动智能开发环境
2. **生产部署**: 使用 `./scripts/docker-deploy.sh deploy` 进行本地Docker部署
3. **远程部署**: 使用 `./scripts/docker-deploy.sh deploy-remote` 部署到远程服务器
4. **数据备份**: 定期运行 `python scripts/backup.py` 备份数据
5. **用户管理**: 通过 `python scripts/create_test_user.py` 创建测试用户
6. **快速提交**: 使用 `./scripts/amend.sh` 进行Git操作

## ⚠️ 注意事项

- 确保脚本有执行权限：`chmod +x scripts/*.sh`
- 部署脚本需要配置SSH密钥访问远程服务器
- 备份脚本会在用户文档目录创建Backup文件夹
- random.py脚本仅适用于macOS系统（使用osascript）
