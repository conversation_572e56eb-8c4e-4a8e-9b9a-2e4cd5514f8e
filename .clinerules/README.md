# Cline 规则文件分类说明

本目录包含项目的所有规则文件，按照功能和用途进行了分类整理，便于团队成员快速查找和使用。

## 分类结构

### 01. 基础规范类
- **01_GENERAL.md** - 项目通用开发规范和基础规则

### 02. 项目文档类
- **02_PROJECT_OVERVIEW.md** - 项目概览、架构和功能特性
- **03_TECHNICAL_DOCS.md** - 详细技术文档（数据模型、API端点、前端模板等）

### 03. 版本管理类
- **04_CHANGELOG.md** - 变更日志规范和更新要求

### 04. 部署运维类
- **05_DEPLOYMENT.md** - 服务器配置和Docker部署指南
- **06_DEVELOPMENT.md** - 开发环境搭建和部署操作规范

### 05. 开发工具类
- **07_SCRIPTS_GUIDE.md** - 项目脚本使用指南和开发工具说明

## 使用说明

每个规则文件都包含了相关领域的详细说明和操作指南，请根据需要查阅相应的文件。

## 维护要求

- 新增规则请按照分类逻辑放置到相应文件中
- 修改规则时请同时更新 CHANGELOG.md 文件
- 保持文件命名的连续性和逻辑性
