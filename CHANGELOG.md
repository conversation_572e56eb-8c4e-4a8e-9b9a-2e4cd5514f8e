# 变更日志

## [Unreleased]

### 功能
- 添加远程服务器部署功能到Docker部署脚本
- 添加Docker开发环境配置，支持在容器中运行开发服务器
- 创建项目操作规则文件，统一管理项目运行和部署规范
- 添加智能开发环境部署脚本，支持自动检测服务状态并智能启动/重启

### 修复
- 修复打开笔记modal时显示"所有标签已清空"的问题
- 修复Docker部署中数据库初始化容器的问题
- 修复Python模块命名冲突问题
- 修复远程服务器上编辑笔记时报错"attempt to write a readonly database"的问题
- 修复Docker部署脚本中的文件权限问题，预防后续部署出现权限错误

### 改进
- 统一 Markdown 编辑器工具栏和编辑器容器的边框样式，将边框移到父容器上
- 简化标签编辑界面，删除冗余的提示文字和图标元素，提升用户体验
- 添加 .clineignore 文件，配置 Cline 忽略规则
- 修改发布笔记的提示语为"记录你的想法和灵感..."，优化用户体验
- 清理 tailwind-custom.css 中重复的 Markdown 样式，避免样式冲突
- 将 random.css 中的 blockquote 样式合并到公共 markdown.css 文件中，统一样式管理
- 删除过时的 login.css 文件，保持项目整洁
- 重构 random.html 页面，完全使用 Tailwind CSS 替代 random.css，移除对随机页面专用样式文件的依赖
- 删除随机笔记界面的「上一条」和「下一条」功能，简化用户界面
- 删除 editTimeWrapper 的 marginTop 样式，优化编辑模态框的布局间距
- 删除 Docker 部署脚本中的测试用户自动创建功能，简化部署流程
- 优化Docker部署配置，移除不必要的数据库初始化容器
- 完善服务器域名访问配置
- 删除 run.sh 脚本，使用 dev-deploy.sh 作为开发环境启动脚本
- 创建综合项目操作规则文件，整合运行和部署规范
- 删除冗余的规则文件（deployment-preferences.md和project-run-preferences.md）
- 重新分类整理 .clinerules 目录中的规则文件，按照功能类别进行组织，便于团队成员查找和使用

## [1.0.0] - 2025-08-02

### 功能
- 初始化项目基础框架
- 实现微博发布、编辑、删除功能
- 添加标签管理和搜索功能
- 实现随机浏览微博功能
- 添加文件上传功能

### 修复
- 

### 改进
-
