# Docker 开发环境使用说明

## 概述

本项目现在支持在 Docker 容器中运行开发环境，确保开发环境和生产环境的一致性。

## 文件说明

- `docker-compose.dev.yml`: Docker Compose 开发环境配置文件
- `Dockerfile.dev`: 开发环境的 Dockerfile，使用 Flask 开发服务器
- `scripts/dev-deploy.sh`: 智能开发环境部署脚本，支持自动检测服务状态

## 使用方法

### 1. 启动 Docker Desktop

首先确保 Docker Desktop 已启动：

```bash
# 检查 Docker 状态
docker info
```

### 2. 使用 dev-deploy.sh 脚本

智能开发环境部署脚本，支持自动检测服务状态并智能决策：

```bash
# 智能部署（自动检测状态并执行相应操作）
./scripts/dev-deploy.sh

# 查看帮助信息
./scripts/dev-deploy.sh help

# 启动开发服务
./scripts/dev-deploy.sh start

# 停止开发服务
./scripts/dev-deploy.sh stop

# 重启开发服务
./scripts/dev-deploy.sh restart

# 查看实时日志
./scripts/dev-deploy.sh logs

# 查看服务状态
./scripts/dev-deploy.sh status
```

### 3. 访问应用

开发环境启动后，应用将在以下地址运行：
- http://localhost:5001

## 开发环境特性

- **热重载**: 使用 Flask 开发服务器，支持代码修改后自动重启
- **环境一致性**: 使用与生产环境相同的依赖和配置
- **文件映射**: 本地代码目录映射到容器中，实时同步修改
- **独立网络**: 使用独立的 Docker 网络，避免端口冲突

## 与传统开发方式的区别

| 特性 | 传统方式 | Docker 方式 |
|------|---------|-------------|
| 环境配置 | 需要手动设置虚拟环境 | 容器化，环境一致 |
| 依赖管理 | 需要手动安装依赖 | 自动安装，版本锁定 |
| 端口管理 | 需要手动清理端口 | 自动端口检查和清理 |
| 部署一致性 | 开发和生产环境可能不同 | 开发和生产环境一致 |

## 注意事项

1. 确保 Docker Desktop 已安装并运行
2. 首次运行需要构建镜像，时间较长
3. 上传文件会保存在 Docker 数据卷中
4. 数据库文件 (`weibo.db`) 会映射到容器中

## 故障排除

### Docker 未运行
```bash
# 检查 Docker 状态
docker info

# 如果 Docker 未运行，启动 Docker Desktop
```

### 端口冲突
脚本会自动检查并清理端口 5001 的占用进程

### 构建失败
```bash
# 清理旧的 Docker 资源
docker system prune

# 重新构建
./scripts/dev-deploy.sh build
```

## 生产部署

生产部署仍然使用原有的 `scripts/docker-deploy.sh` 脚本，使用 gunicorn 作为生产服务器。
