# 简单笔记应用

这是一个使用 Flask 和 SQLAlchemy 构建的简单笔记 Web 应用。

## 功能

*   用户注册和登录
*   发布、查看、编辑和删除笔记
*   为笔记添加标签
*   按标签过滤笔记
*   搜索笔记内容和标签
*   文件上传（最大 100MB）

## 技术栈

*   **后端:** Python, Flask, SQLAlchemy
*   **数据库:** SQLite
*   **前端:** HTML, CSS, JavaScript (通过 `templates/` 和 `static/` 目录)

## 安装

1.  **克隆仓库:**
    ```bash
    git clone <your-repository-url>
    cd <your-repository-directory>
    ```

2.  **创建并激活虚拟环境:**
    ```bash
    python -m venv venv
    # Windows
    venv\Scripts\activate
    # macOS/Linux
    source venv/bin/activate
    ```

3.  **安装 Python 依赖:**
    ```bash
    pip install -r requirements.txt
    ```

## 运行

### 传统方式
1.  **启动 Flask 应用:**
    ```bash
    flask run --host=0.0.0.0 --port=5001
    ```
    或者直接运行主脚本：
    ```bash
    python app.py
    ```
    应用将在 `http://localhost:5001` (或配置的其他端口) 上运行。

2.  **数据库初始化:**
    应用首次运行时会自动创建 `weibo.db` 数据库文件和所需的表。

### Docker 开发环境（推荐）
使用 Docker 容器化开发环境，确保环境一致性：

```bash
# 智能部署（自动检测状态并执行相应操作）
./scripts/dev-deploy.sh
```

应用将在 `http://localhost:5001` 上运行。

## 使用

### 快速开始

1. 访问 `http://localhost:5001/`
2. 使用以下测试账户登录：

### 测试账户信息

| 用户名 | 密码 | 说明 |
|--------|------|------|
| `admin` | `password` | 管理员测试账户 |

### 使用说明

*   使用上述测试账户登录后即可开始使用笔记功能
*   可以发布、编辑、删除笔记
*   支持为笔记添加标签和搜索功能
*   支持文件上传（最大100MB）

> **注意**: 目前代码中没有用户注册功能，如需添加新用户请手动在数据库中添加或修改代码实现注册逻辑。

## 目录结构

```
.
├── app.py            # Flask 应用主文件
├── models.py         # SQLAlchemy 数据模型
├── requirements.txt  # Python 依赖
├── templates/        # HTML 模板
├── static/           # 静态文件 (CSS, JS, Images)
├── uploads/          # 文件上传目录 (自动创建)
├── weibo.db          # SQLite 数据库文件 (自动创建)
├── venv/             # Python 虚拟环境 (本地)
└── README.md         # 本文件
```

## 注意

*   默认的 `secret_key` 用于开发环境，生产环境请务必更换。
*   当前没有用户注册功能，需要手动在数据库中添加用户或在代码中实现注册逻辑才能登录。
