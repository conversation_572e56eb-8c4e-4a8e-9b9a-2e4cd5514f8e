services:
  weibo-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: weibo-dev
    ports:
      - "5001:5001"
    environment:
      - FLASK_ENV=development
      - FLASK_DEBUG=1
    volumes:
      - .:/app
      - weibo-dev-data:/app/uploads
      - ./weibo.db:/app/weibo.db
    restart: unless-stopped
    networks:
      - weibo-dev-network
    stdin_open: true
    tty: true

volumes:
  weibo-dev-data:
    name: weibo-dev-uploads-data

networks:
  weibo-dev-network:
    driver: bridge
