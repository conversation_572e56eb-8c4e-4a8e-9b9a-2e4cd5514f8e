#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
服务器数据库备份脚本
从远程服务器复制数据库文件到本地备份目录
"""

import os
import shutil
from datetime import datetime
import subprocess
import sys

def backup_server_database():
    """从服务器备份数据库文件到本地"""
    
    # 服务器配置
    SERVER_USER = "lighthouse"
    SERVER_HOST = "**************"
    SERVER_PORT = "22"
    REMOTE_DIR = "/home/<USER>/flask/weibo"
    REMOTE_DB_PATH = f"{REMOTE_DIR}/weibo.db"
    
    # 本地备份配置
    backup_dir = os.path.expanduser('~/Documents/Backup')
    os.makedirs(backup_dir, exist_ok=True)
    
    # 生成带时间戳的备份文件名
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    local_backup_file = os.path.join(backup_dir, f'weibo_server_{timestamp}.db')
    
    try:
        print(f"正在从服务器 {SERVER_HOST} 备份数据库...")
        print(f"服务器数据库路径: {REMOTE_DB_PATH}")
        
        # 使用 scp 命令从服务器复制数据库文件
        scp_command = [
            'scp',
            '-P', SERVER_PORT,
            f'{SERVER_USER}@{SERVER_HOST}:{REMOTE_DB_PATH}',
            local_backup_file
        ]
        
        print(f"执行命令: {' '.join(scp_command)}")
        result = subprocess.run(scp_command, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ 服务器数据库已成功备份到: {local_backup_file}")
            # 显示备份文件信息
            if os.path.exists(local_backup_file):
                file_size = os.path.getsize(local_backup_file)
                print(f"备份文件大小: {file_size} 字节 ({file_size/1024/1024:.2f} MB)")
            return True
        else:
            print(f"❌ 备份失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 备份过程中出现错误: {str(e)}")
        return False

def main():
    """主函数"""
    print("=== 服务器数据库备份工具 ===")
    
    # 检查是否安装了 scp 命令
    if not shutil.which('scp'):
        print("❌ 错误: 系统中未找到 scp 命令，请确保已安装 OpenSSH 客户端")
        sys.exit(1)
    
    # 执行备份
    success = backup_server_database()
    
    if success:
        print("\n🎉 备份完成!")
        sys.exit(0)
    else:
        print("\n💥 备份失败!")
        sys.exit(1)

if __name__ == '__main__':
    main()
