#!/bin/bash

# 项目远程部署脚本
# 封装 docker-deploy.sh 的远程部署功能，提供简洁的部署入口

# 显示帮助信息
function show_help() {
    echo "微博应用远程部署脚本"
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  deploy       部署到远程服务器"
    echo "  help         显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 deploy    # 部署到远程服务器"
}

# 主部署函数
function deploy() {
    echo "🚀 开始远程部署到服务器..."
    echo "📦 正在调用 docker-deploy.sh deploy-remote..."
    
    # 直接调用现有的远程部署功能
    bash scripts/docker-deploy.sh deploy-remote
    
    # 检查执行状态
    if [ $? -eq 0 ]; then
        echo "✅ 远程部署脚本执行完成"
    else
        echo "❌ 远程部署失败，请检查错误信息"
        exit 1
    fi
}

# 主函数
function main() {
    case "$1" in
        deploy)
            deploy
            ;;
        help)
            show_help
            ;;
        *)
            # 默认行为：直接执行部署
            deploy
            ;;
    esac
}

# 执行主函数
main "$@"
