#!/bin/bash

# 开发环境部署脚本
# 用于智能启动或重启开发环境的Docker服务

# 颜色输出函数
function echo_info() {
    echo -e "\033[34m[INFO] $1\033[0m"
}

function echo_success() {
    echo -e "\033[32m[SUCCESS] $1\033[0m"
}

function echo_warning() {
    echo -e "\033[33m[WARNING] $1\033[0m"
}

function echo_error() {
    echo -e "\033[31m[ERROR] $1\033[0m"
}

# 检查命令是否执行成功
function check_status() {
    if [ $? -eq 0 ]; then
        echo_success "$1"
    else
        echo_error "$2"
        exit 1
    fi
}

# 检查Docker是否安装和运行
function check_docker() {
    if ! command -v docker &> /dev/null; then
        echo_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        echo_error "docker-compose未安装，请先安装docker-compose"
        exit 1
    fi
    
    # 检查Docker守护进程是否运行
    if ! docker info &> /dev/null; then
        echo_error "Docker守护进程未运行，请先启动Docker"
        exit 1
    fi
    
    echo_info "Docker环境检查通过"
}

# 检查开发服务状态
function check_dev_service_status() {
    echo_info "检查开发服务状态..."
    local status_output=$(docker-compose -f docker-compose.dev.yml ps 2>/dev/null)
    
    if echo "$status_output" | grep -q "weibo-dev" && echo "$status_output" | grep -q "Up"; then
        echo_info "开发服务正在运行"
        return 0  # 服务正在运行
    else
        echo_info "开发服务未运行"
        return 1  # 服务未运行
    fi
}

# 启动开发服务
function start_dev_services() {
    echo_info "启动开发服务..."
    docker-compose -f docker-compose.dev.yml up -d
    check_status "开发服务启动成功" "开发服务启动失败"
}

# 重启开发服务
function restart_dev_services() {
    echo_info "重启开发服务..."
    docker-compose -f docker-compose.dev.yml restart
    check_status "开发服务重启成功" "开发服务重启失败"
}

# 查看开发日志
function show_dev_logs() {
    echo_info "查看开发服务日志..."
    docker-compose -f docker-compose.dev.yml logs -f
}

# 停止开发服务
function stop_dev_services() {
    echo_info "停止开发服务..."
    docker-compose -f docker-compose.dev.yml down
    check_status "开发服务停止成功" "开发服务停止失败"
}

# 构建开发镜像
function build_dev_images() {
    echo_info "构建开发镜像..."
    docker-compose -f docker-compose.dev.yml build
    check_status "开发镜像构建成功" "开发镜像构建失败"
}

# 智能部署开发环境
function deploy_dev() {
    echo_info "=== 开始智能部署开发环境 ==="
    
    # 检查Docker环境
    check_docker
    
    # 检查服务状态
    if check_dev_service_status; then
        # 服务正在运行，执行重启
        echo_info "服务已运行，执行重启操作..."
        restart_dev_services
    else
        # 服务未运行，执行启动
        echo_info "服务未运行，执行启动操作..."
        start_dev_services
    fi
    
    echo_success "=== 开发环境部署完成! ==="
    echo_info "应用将在 http://localhost:5001 上运行"
    echo_info "使用 '$0 logs' 查看实时日志"
    echo_info "使用 '$0 stop' 停止服务"
}

# 显示帮助信息
function show_help() {
    echo "开发环境智能部署脚本"
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  deploy       智能部署开发环境（默认）"
    echo "  start        启动开发服务"
    echo "  stop         停止开发服务"
    echo "  restart      重启开发服务"
    echo "  logs         查看开发日志"
    echo "  build        构建开发镜像"
    echo "  status       查看服务状态"
    echo "  help         显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0            # 智能部署开发环境"
    echo "  $0 logs       # 查看实时日志"
    echo "  $0 status     # 查看服务状态"
}

# 显示服务状态
function show_status() {
    echo_info "开发服务状态:"
    docker-compose -f docker-compose.dev.yml ps
}

# 主函数
function main() {
    case "${1:-deploy}" in
        deploy)
            deploy_dev
            ;;
        start)
            check_docker
            start_dev_services
            ;;
        stop)
            check_docker
            stop_dev_services
            ;;
        restart)
            check_docker
            restart_dev_services
            ;;
        logs)
            check_docker
            show_dev_logs
            ;;
        build)
            check_docker
            build_dev_images
            ;;
        status)
            check_docker
            show_status
            ;;
        help|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
