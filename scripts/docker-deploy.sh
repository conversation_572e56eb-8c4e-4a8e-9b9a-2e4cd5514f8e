#!/bin/bash

# Docker部署脚本
# 用于构建和部署微博应用的Docker容器

# 颜色输出函数
function echo_info() {
    echo -e "\033[34m[INFO] $1\033[0m"
}

function echo_success() {
    echo -e "\033[32m[SUCCESS] $1\033[0m"
}

function echo_error() {
    echo -e "\033[31m[ERROR] $1\033[0m"
}

# 检查命令是否执行成功
function check_status() {
    if [ $? -eq 0 ]; then
        echo_success "$1"
    else
        echo_error "$2"
        exit 1
    fi
}

# 检查Docker是否安装
function check_docker() {
    if ! command -v docker &> /dev/null; then
        echo_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        echo_error "docker-compose未安装，请先安装docker-compose"
        exit 1
    fi
    
    echo_info "Docker环境检查通过"
}

# 构建Docker镜像
function build_images() {
    echo_info "开始构建Docker镜像..."
    docker-compose build
    check_status "Docker镜像构建成功" "Docker镜像构建失败"
}

# 启动服务
function start_services() {
    echo_info "启动Docker服务..."
    docker-compose up -d
    check_status "Docker服务启动成功" "Docker服务启动失败"
}

# 停止服务
function stop_services() {
    echo_info "停止Docker服务..."
    docker-compose down
    check_status "Docker服务停止成功" "Docker服务停止失败"
}

# 重启服务
function restart_services() {
    echo_info "重启Docker服务..."
    docker-compose restart
    check_status "Docker服务重启成功" "Docker服务重启失败"
}

# 查看服务状态
function show_status() {
    echo_info "Docker服务状态:"
    docker-compose ps
}

# 查看日志
function show_logs() {
    echo_info "查看Docker服务日志:"
    docker-compose logs -f
}

# 修复文件权限
function fix_permissions() {
    echo_info "修复文件权限..."
    
    # 修复数据库文件权限
    if [ -f "weibo.db" ]; then
        echo_info "修复数据库文件权限..."
        sudo chown 1000:1000 weibo.db 2>/dev/null || true
        sudo chmod 644 weibo.db 2>/dev/null || true
    fi
    
    # 修复上传目录权限
    if [ -d "uploads" ]; then
        echo_info "修复上传目录权限..."
        sudo chown -R 1000:1000 uploads 2>/dev/null || true
        sudo chmod -R 755 uploads 2>/dev/null || true
    fi
    
    echo_success "文件权限修复完成"
}

# 初始化数据库
function init_database() {
    echo_info "初始化数据库..."
    # 检查数据库文件是否存在
    if [ ! -f "weibo.db" ]; then
        echo_info "数据库文件不存在，开始初始化..."
        python3 scripts/init_db.py
        check_status "数据库初始化成功" "数据库初始化失败"
    else
        echo_info "数据库文件已存在，跳过初始化"
    fi
    
    # 确保数据库文件权限正确
    fix_permissions
}


# 服务器配置
SERVER_USER="lighthouse"
SERVER_HOST="**************"
SERVER_PORT="22"
REMOTE_DIR="/home/<USER>/flask/weibo"

# 显示帮助信息
function show_help() {
    echo "微博应用Docker部署脚本"
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  build        构建Docker镜像"
    echo "  up           启动服务"
    echo "  down         停止服务"
    echo "  restart      重启服务"
    echo "  status       查看服务状态"
    echo "  logs         查看日志"
    echo "  init-db      初始化数据库"
    echo "  deploy       完整部署流程 (构建+启动+初始化)"
    echo "  deploy-remote 部署到远程服务器"
    echo "  help         显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 deploy        # 本地完整部署"
    echo "  $0 deploy-remote # 部署到远程服务器"
    echo "  $0 up            # 启动服务"
    echo "  $0 logs          # 查看实时日志"
}

# 远程部署到服务器
function deploy_remote() {
    echo_info "=== 开始远程部署到服务器 ==="
    
    # 检查必要的命令
    if ! command -v rsync &> /dev/null; then
        echo_error "rsync未安装，请先安装rsync"
        exit 1
    fi
    
    # 确保远程目录存在
    echo_info "创建远程部署目录..."
    ssh -p $SERVER_PORT $SERVER_USER@$SERVER_HOST "mkdir -p $REMOTE_DIR"
    check_status "远程目录创建成功" "远程目录创建失败"
    
    # 同步项目文件到服务器（排除数据库文件）
    echo_info "同步项目文件到服务器..."
    rsync -avz --exclude '.git' \
        --exclude '__pycache__' \
        --exclude '*.pyc' \
        --exclude 'venv/' \
        --exclude '*.db' \
        --exclude 'node_modules/' \
        --exclude 'uploads/' \
        -e "ssh -p $SERVER_PORT" \
        ./ $SERVER_USER@$SERVER_HOST:$REMOTE_DIR/
    check_status "文件同步成功" "文件同步失败"
    
    # 在服务器上修复文件权限
    echo_info "在服务器上修复文件权限..."
    ssh -p $SERVER_PORT $SERVER_USER@$SERVER_HOST "cd $REMOTE_DIR && sudo chown 1000:1000 weibo.db 2>/dev/null || true"
    ssh -p $SERVER_PORT $SERVER_USER@$SERVER_HOST "cd $REMOTE_DIR && sudo chmod 644 weibo.db 2>/dev/null || true"
    ssh -p $SERVER_PORT $SERVER_USER@$SERVER_HOST "cd $REMOTE_DIR && sudo chown -R 1000:1000 uploads 2>/dev/null || true"
    ssh -p $SERVER_PORT $SERVER_USER@$SERVER_HOST "cd $REMOTE_DIR && sudo chmod -R 755 uploads 2>/dev/null || true"
    
    # 在服务器上执行Docker部署
    echo_info "在服务器上执行Docker部署..."
    ssh -p $SERVER_PORT $SERVER_USER@$SERVER_HOST "cd $REMOTE_DIR && bash scripts/docker-deploy.sh deploy"
    check_status "远程Docker部署成功" "远程Docker部署失败"
    
    echo_success "=== 远程部署完成! ==="
    echo_info "应用访问地址: http://$SERVER_HOST:5001"
}

# 完整部署流程
function deploy() {
    echo_info "=== 开始完整部署流程 ==="
    
    # 检查Docker环境
    check_docker
    
    # 修复文件权限
    fix_permissions
    
    # 初始化数据库
    init_database
    
    # 构建镜像
    build_images
    
    # 启动服务
    start_services
    
    echo_success "=== 部署完成! ==="
    echo_info "应用访问地址: http://localhost:5001"
}

# 主函数
function main() {
    case "$1" in
        build)
            check_docker
            build_images
            ;;
        up)
            check_docker
            start_services
            ;;
        down)
            check_docker
            stop_services
            ;;
        restart)
            check_docker
            restart_services
            ;;
        status)
            check_docker
            show_status
            ;;
        logs)
            check_docker
            show_logs
            ;;
        init-db)
            init_database
            ;;
        deploy)
            deploy
            ;;
        deploy-remote)
            deploy_remote
            ;;
        help|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
