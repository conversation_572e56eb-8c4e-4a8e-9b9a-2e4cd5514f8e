/* 设计令牌系统 - 统一的设计变量定义 */

:root {
    /* ========== 颜色系统 ========== */
    
    /* 主色调 - 靛蓝到紫色渐变 */
    --color-primary-50: #eff6ff;
    --color-primary-100: #dbeafe;
    --color-primary-200: #bfdbfe;
    --color-primary-300: #93c5fd;
    --color-primary-400: #60a5fa;
    --color-primary-500: #6366f1;
    --color-primary-600: #4f46e5;
    --color-primary-700: #4338ca;
    --color-primary-800: #3730a3;
    --color-primary-900: #312e81;
    
    /* 紫色辅助色 */
    --color-purple-50: #faf5ff;
    --color-purple-100: #f3e8ff;
    --color-purple-200: #e9d5ff;
    --color-purple-300: #d946ef;
    --color-purple-500: #a855f7;
    --color-purple-600: #8b5cf6;
    --color-purple-700: #7c3aed;
    
    /* 灰度色系 */
    --color-gray-50: #f9fafb;
    --color-gray-100: #f3f4f6;
    --color-gray-200: #e5e7eb;
    --color-gray-300: #d1d5db;
    --color-gray-400: #9ca3af;
    --color-gray-500: #6b7280;
    --color-gray-600: #4b5563;
    --color-gray-700: #374151;
    --color-gray-800: #1f2937;
    --color-gray-900: #111827;
    
    /* 功能色系 */
    --color-success-50: #dcfce7;
    --color-success-100: #bbf7d0;
    --color-success-200: #86efac;
    --color-success-300: #4ade80;
    --color-success-400: #22c55e;
    --color-success-500: #16a34a;
    --color-success-600: #15803d;
    --color-success-700: #166534;
    --color-success-800: #14532d;
    --color-success-900: #0f172a;
    
    /* 绿宝石色系 - 用于随机按钮 */
    --color-emerald-50: #ecfdf5;
    --color-emerald-100: #d1fae5;
    --color-emerald-200: #a7f3d0;
    --color-emerald-300: #6ee7b7;
    --color-emerald-400: #34d399;
    --color-emerald-500: #10b981;
    --color-emerald-600: #059669;
    --color-emerald-700: #047857;
    --color-emerald-800: #065f46;
    --color-emerald-900: #064e3b;
    
    --color-warning-50: #fef3c7;
    --color-warning-500: #f59e0b;
    --color-warning-600: #d97706;
    --color-warning-700: #92400e;
    
    --color-error-50: #fee2e2;
    --color-error-100: #fecaca;
    --color-error-500: #ef4444;
    --color-error-600: #dc2626;
    --color-error-700: #991b1b;
    
    --color-info-50: #dbeafe;
    --color-info-500: #3b82f6;
    --color-info-600: #2563eb;
    --color-info-700: #1e40af;
    
    /* 背景色 */
    --color-background: #ffffff;
    --color-background-secondary: #f8fafc;
    --color-background-tertiary: #f1f5f9;
    
    /* 文本色 */
    --color-text-primary: var(--color-gray-800);
    --color-text-secondary: var(--color-gray-600);
    --color-text-tertiary: var(--color-gray-500);
    --color-text-inverse: #ffffff;
    
    /* ========== 间距系统 ========== */
    --space-0: 0;
    --space-1: 0.25rem;   /* 4px */
    --space-2: 0.5rem;    /* 8px */
    --space-3: 0.75rem;   /* 12px */
    --space-4: 1rem;      /* 16px */
    --space-5: 1.25rem;   /* 20px */
    --space-6: 1.5rem;    /* 24px */
    --space-8: 2rem;      /* 32px */
    --space-10: 2.5rem;   /* 40px */
    --space-12: 3rem;     /* 48px */
    --space-16: 4rem;     /* 64px */
    --space-20: 5rem;     /* 80px */
    
    /* 响应式间距 */
    --space-responsive-sm: clamp(var(--space-2), 2vw, var(--space-4));
    --space-responsive-md: clamp(var(--space-4), 4vw, var(--space-8));
    --space-responsive-lg: clamp(var(--space-6), 6vw, var(--space-12));
    
    /* ========== 字体系统 ========== */
    --font-family-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --font-family-mono: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    
    /* 字体大小 */
    --text-xs: 0.75rem;     /* 12px */
    --text-sm: 0.875rem;    /* 14px */
    --text-base: 1rem;      /* 16px */
    --text-lg: 1.125rem;    /* 18px */
    --text-xl: 1.25rem;     /* 20px */
    --text-2xl: 1.5rem;     /* 24px */
    --text-3xl: 1.875rem;   /* 30px */
    
    /* 响应式字体大小 */
    --text-responsive-sm: clamp(var(--text-xs), 2.5vw, var(--text-sm));
    --text-responsive-base: clamp(var(--text-sm), 3vw, var(--text-base));
    --text-responsive-lg: clamp(var(--text-base), 3.5vw, var(--text-lg));
    
    /* 字重 */
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    
    /* 行高 */
    --line-height-tight: 1.25;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.75;
    
    /* ========== 边框和圆角 ========== */
    --border-width: 1px;
    --border-width-thick: 2px;
    
    --border-radius-none: 0;
    --border-radius-sm: 0.25rem;   /* 4px */
    --border-radius-md: 0.5rem;    /* 8px */
    --border-radius-lg: 0.75rem;   /* 12px */
    --border-radius-xl: 1rem;      /* 16px */
    --border-radius-2xl: 1.5rem;   /* 24px */
    --border-radius-full: 9999px;
    
    /* 响应式圆角 */
    --border-radius-responsive: clamp(var(--border-radius-md), 2vw, var(--border-radius-xl));
    
    /* ========== 阴影系统 ========== */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    
    /* 彩色阴影 */
    --shadow-primary: 0 4px 6px -1px rgba(99, 102, 241, 0.25), 0 2px 4px -1px rgba(99, 102, 241, 0.1);
    --shadow-primary-lg: 0 10px 15px -3px rgba(99, 102, 241, 0.3), 0 4px 6px -2px rgba(99, 102, 241, 0.15);
    
    /* ========== 过渡和动画 ========== */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.2s ease;
    --transition-slow: 0.3s ease;
    --transition-slower: 0.5s ease;
    
    --ease-in: cubic-bezier(0.4, 0, 1, 1);
    --ease-out: cubic-bezier(0, 0, 0.2, 1);
    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
    
    /* ========== Z-index 层级 ========== */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --z-toast: 1080;
    
    /* ========== 组件特定变量 ========== */
    
    /* 标签组件 */
    --tag-height: 2rem;
    --tag-padding-x: var(--space-3);
    --tag-padding-y: var(--space-2);
    --tag-font-size: var(--text-xs);
    --tag-border-radius: var(--border-radius-lg);
    
    /* 按钮组件 */
    --button-height-sm: 2rem;
    --button-height-md: 2.5rem;
    --button-height-lg: 3rem;
    --button-padding-x: var(--space-4);
    --button-border-radius: var(--border-radius-xl);
    
    /* 悬浮按钮组件 */
    --fab-size: 3.5rem;           /* 56px */
    --fab-size-mobile: 3rem;      /* 48px */
    --fab-bottom: var(--space-8); /* 32px */
    --fab-side: var(--space-8);   /* 32px */
    --fab-bottom-mobile: var(--space-6); /* 24px */
    --fab-side-mobile: var(--space-6);   /* 24px */
    --fab-shadow: var(--shadow-lg);
    --fab-shadow-hover: var(--shadow-xl);
    --fab-transition: var(--transition-normal);
    --fab-z-index: var(--z-fixed);
    
    /* 模态框组件 */
    --modal-max-width: min(95vw, 1200px);
    --modal-min-width: 600px;
    --modal-margin: clamp(1vh, 5vw, 10vh);
    --modal-border-radius: var(--border-radius-2xl);
    
    /* 导航栏组件 */
    --navbar-height: 60px;
    --navbar-height-mobile: 50px;
    --navbar-padding: var(--space-8);
    --navbar-padding-mobile: var(--space-4);
    
    /* ========== 断点系统 ========== */
    --breakpoint-sm: 640px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 1024px;
    --breakpoint-xl: 1280px;
    --breakpoint-2xl: 1536px;
}

/* 深色模式支持（预留） */
@media (prefers-color-scheme: dark) {
    :root {
        --color-background: #1f2937;
        --color-background-secondary: #374151;
        --color-background-tertiary: #4b5563;
        --color-text-primary: #f9fafb;
        --color-text-secondary: #d1d5db;
        --color-text-tertiary: #9ca3af;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    :root {
        --border-width: var(--border-width-thick);
        --shadow-sm: 0 2px 4px 0 rgba(0, 0, 0, 0.2);
        --shadow-md: 0 6px 12px -2px rgba(0, 0, 0, 0.25);
    }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
    :root {
        --transition-fast: 0.01ms;
        --transition-normal: 0.01ms;
        --transition-slow: 0.01ms;
        --transition-slower: 0.01ms;
    }
    
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}