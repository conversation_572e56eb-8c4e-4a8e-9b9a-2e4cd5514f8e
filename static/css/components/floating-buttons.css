/* 悬浮按钮组件样式 - 使用设计令牌系统 */

/* 悬浮按钮基础样式 */
.floating-action-button {
    position: fixed;
    width: var(--fab-size);
    height: var(--fab-size);
    border-radius: var(--border-radius-full);
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--fab-transition) ease-out;
    z-index: var(--fab-z-index);
    box-shadow: var(--fab-shadow);
    font-size: 0; /* 隐藏文本，只显示图标 */
}

.floating-action-button:hover {
    transform: scale(1.1);
    box-shadow: var(--fab-shadow-hover);
}

.floating-action-button:active {
    transform: scale(0.95);
}

.floating-action-button svg {
    width: 1.5rem;
    height: 1.5rem;
    transition: transform var(--transition-fast);
}

/* 位置变体 */
.floating-action-button--bottom-left {
    bottom: var(--fab-bottom);
    left: var(--fab-side);
}

.floating-action-button--bottom-right {
    bottom: var(--fab-bottom);
    right: var(--fab-side);
}

/* 垂直按钮组 - 上方按钮位置 */
.floating-action-button--bottom-right-upper {
    bottom: calc(var(--fab-bottom) + var(--fab-size) + var(--space-4)); /* 在下方按钮上方，间距16px */
    right: var(--fab-side);
}

/* 颜色变体 */
.floating-action-button--primary {
    background: linear-gradient(135deg, var(--color-primary-500) 0%, var(--color-purple-600) 100%);
    color: var(--color-text-inverse);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.floating-action-button--primary:hover {
    box-shadow: 0 8px 20px rgba(99, 102, 241, 0.4);
}

.floating-action-button--success {
    background: linear-gradient(135deg, var(--color-success-500) 0%, var(--color-emerald-600) 100%);
    color: var(--color-text-inverse);
    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
}

.floating-action-button--success:hover {
    box-shadow: 0 8px 20px rgba(34, 197, 94, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .floating-action-button {
        width: var(--fab-size-mobile);
        height: var(--fab-size-mobile);
    }
    
    .floating-action-button--bottom-left {
        bottom: var(--fab-bottom-mobile);
        left: var(--fab-side-mobile);
    }
    
    .floating-action-button--bottom-right {
        bottom: var(--fab-bottom-mobile);
        right: var(--fab-side-mobile);
    }
    
    .floating-action-button--bottom-right-upper {
        bottom: calc(var(--fab-bottom-mobile) + var(--fab-size-mobile) + var(--space-3)); /* 移动端间距稍小 */
        right: var(--fab-side-mobile);
    }
    
    .floating-action-button svg {
        width: 1.25rem;
        height: 1.25rem;
    }
}

/* 动画效果 */
@keyframes fab-bounce-in {
    0% {
        transform: scale(0) rotate(-360deg);
        opacity: 0;
    }
    50% {
        transform: scale(1.2) rotate(-180deg);
        opacity: 0.8;
    }
    100% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
}

.floating-action-button--animate-in {
    animation: fab-bounce-in 0.6s ease-out;
}

/* 脉冲动画（可选，用于吸引注意力） */
@keyframes fab-pulse {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.7);
    }
    50% {
        box-shadow: 0 0 0 8px rgba(99, 102, 241, 0);
    }
}

.floating-action-button--pulse {
    animation: fab-pulse 2s infinite;
}

/* 确保在小屏幕上不会与内容重叠 */
@media (max-width: 640px) {
    .floating-action-button {
        width: 2.75rem;
        height: 2.75rem;
    }
    
    .floating-action-button--bottom-left {
        bottom: 1rem;
        left: 1rem;
    }
    
    .floating-action-button--bottom-right {
        bottom: 1rem;
        right: 1rem;
    }
    
    .floating-action-button--bottom-right-upper {
        bottom: calc(1rem + 2.75rem + 0.75rem); /* 超小屏幕的垂直间距 */
        right: 1rem;
    }
    
    .floating-action-button svg {
        width: 1rem;
        height: 1rem;
    }
}

/* 无障碍支持 */
.floating-action-button:focus {
    outline: 2px solid var(--color-primary-500);
    outline-offset: 2px;
}

.floating-action-button:focus:not(:focus-visible) {
    outline: none;
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
    .floating-action-button {
        transition: none;
    }
    
    .floating-action-button:hover {
        transform: none;
    }
    
    .floating-action-button:active {
        transform: none;
    }
    
    .floating-action-button--animate-in {
        animation: none;
    }
    
    .floating-action-button--pulse {
        animation: none;
    }
}