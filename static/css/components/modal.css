/* 模态框组件样式 - 使用设计令牌系统 */

/* 模态框显示状态 */
.modal {
    display: none;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 模态框动画 */
@keyframes fade-in {
    from { 
        opacity: 0; 
    }
    to { 
        opacity: 1; 
    }
}

@keyframes slide-in {
    from {
        opacity: 0;
        transform: translateY(var(--space-8));
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes modal-slide-in {
    from {
        transform: translateY(calc(-1 * var(--space-12)));
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* 动画类 */
.animate-fade-in {
    animation: fade-in var(--transition-slow) var(--ease-out);
}

.animate-slide-in {
    animation: slide-in var(--transition-slower) var(--ease-out);
}

.animate-modal-slide-in {
    animation: modal-slide-in var(--transition-slow) var(--ease-out);
}

/* 模态框宽度设置 - 桌面端 */
.modal .bg-white {
    min-width: var(--modal-min-width);
    max-width: var(--modal-max-width);
    border-radius: var(--modal-border-radius);
}

/* 笔记模态框特定样式 */
/* 编辑器容器 */
#postModal .editor-container {
    position: relative;
    border: 0;
    border-radius: 0 0 var(--border-radius-xl) var(--border-radius-xl);
    overflow: hidden;
}

/* 标签输入包装器 */
#postModal .tags-input-wrapper {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2);
    border: var(--border-width) solid var(--color-gray-200);
    border-radius: var(--border-radius-md);
    background: var(--color-background);
    transition: all var(--transition-slow) var(--ease-out);
    box-shadow: var(--shadow-sm);
    margin-top: var(--space-2);
}

#postModal .tags-input-wrapper:focus-within {
    border-color: var(--color-primary-500);
    box-shadow: 0 0 0 var(--space-1) rgba(99, 102, 241, 0.1), 0 var(--space-1) var(--space-2) rgba(99, 102, 241, 0.15);
    outline: none;
}

/* 编辑器聚焦状态 */
#postModal .border-2.border-gray-200:focus-within {
    border-color: var(--color-primary-400);
    box-shadow: 0 0 0 var(--space-1) rgba(102, 126, 234, 0.1);
}

/* 日期时间输入框基础样式 */
#postTimeInput {
    max-width: 100%;
    box-sizing: border-box;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 统一模态框关闭按钮样式 */
.modal .close-button,
.modal button[data-dismiss="modal"]:not(.custom-modal-button) {
    width: var(--space-8);
    height: var(--space-8);
    min-width: var(--space-8);
    min-height: var(--space-8);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius-full);
    transition: all var(--transition-normal) var(--ease-out);
    font-weight: var(--font-weight-normal);
    border: none;
    background: var(--color-gray-100);
    color: var(--color-text-secondary);
    cursor: pointer;
}

.modal .close-button:hover,
.modal button[data-dismiss="modal"]:not(.custom-modal-button):hover {
    transform: scale(1.1);
    background: var(--color-gray-200);
    color: var(--color-text-primary);
}

.modal .close-button:focus,
.modal button[data-dismiss="modal"]:not(.custom-modal-button):focus {
    outline: var(--border-width-thick) solid var(--color-primary-500);
    outline-offset: var(--space-1);
}

/* ========== 响应式样式 ========== */

/* 统一使用设计令牌中的断点 */

/* 桌面端样式 (>= 1024px) */
@media (min-width: 1024px) {
    /* 模态框尺寸 - 桌面端 */
    .modal .bg-white {
        width: 90%;
        min-width: var(--modal-min-width);
        max-width: var(--modal-max-width);
        margin: var(--modal-margin);
        max-height: 85vh;
    }

    /* 时间输入区域 - 桌面端 */
    #postTimeInput {
        padding-right: 1.75rem;
    }

    #setCurrentTimeBtn {
        position: absolute;
        right: var(--space-2);
        top: 50%;
        transform: translateY(-50%);
        padding: var(--space-1);
    }
}

/* 中等屏幕尺寸优化 (769px - 1024px) */
@media (max-width: 1024px) and (min-width: 769px) {
    #editTimeWrapper .flex.items-center.justify-end {
        justify-content: flex-start;
    }

    #editTimeWrapper .flex.items-center.gap-2 {
        width: 100%;
        max-width: none;
        flex-wrap: nowrap;
        justify-content: flex-start;
    }

    #postTimeInput {
        flex: 1;
        min-width: 180px;
        max-width: none;
        width: auto;
        font-size: var(--text-sm);
        padding: var(--space-2) var(--space-3);
        padding-right: 1.75rem;
    }

    #setCurrentTimeBtn {
        position: absolute;
        right: var(--space-2);
        top: 50%;
        transform: translateY(-50%);
        padding: var(--space-1);
        flex-shrink: 0;
    }
}

/* 中小屏幕尺寸优化 (481px - 768px) */
@media (max-width: 768px) and (min-width: 481px) {
    #editTimeWrapper .flex.items-center.gap-2 {
        flex-wrap: nowrap;
        justify-content: flex-start;
    }

    #postTimeInput {
        flex: 1;
        min-width: 160px;
        max-width: none;
        width: auto;
        padding-right: 1.5rem;
    }

    #setCurrentTimeBtn {
        position: absolute;
        right: var(--space-1);
        top: 50%;
        transform: translateY(-50%);
        padding: var(--space-1);
        flex-shrink: 0;
    }
}

/* 平板端样式 (768px - 1023px) */
@media (min-width: 768px) and (max-width: 1023px) {
    /* 模态框尺寸 - 平板端 */
    .modal .bg-white {
        width: 90%;
        min-width: 320px;
        max-width: 90vw;
        margin: clamp(2vh, 4vw, 8vh);
        max-height: 88vh;
    }

    /* 时间输入区域 - 平板端 */
    #editTimeWrapper {
        padding: var(--space-3) var(--space-4);
    }

    #postTimeInput {
        padding-right: 1.5rem;
        font-size: 0.875rem;
    }

    #setCurrentTimeBtn {
        position: absolute;
        right: var(--space-1);
        top: 50%;
        transform: translateY(-50%);
        padding: var(--space-1);
    }
}

/* 移动端优化 (max-width: 767px) */
@media (max-width: 767px) {
    /* 模态框尺寸调整 */
    .modal .bg-white {
        margin: var(--space-2);
        width: calc(100% - var(--space-4));
        min-width: 280px;
        max-width: calc(100% - var(--space-4));
        max-height: calc(100vh - var(--space-4));
    }

    /* 模态框头部优化 */
    #postModal .flex.justify-between.items-center {
        padding: var(--space-3) var(--space-4);
    }

    #postModal #modalTitle {
        font-size: 1rem;
        padding: 0 var(--space-2);
    }

    /* 时间输入区域移动端优化 */
    #editTimeWrapper {
        padding: var(--space-2) var(--space-4);
    }

    #editTimeWrapper .flex.items-center.justify-end {
        justify-content: stretch;
    }

    #editTimeWrapper .flex.items-center.gap-2 {
        flex-direction: column;
        align-items: stretch;
        gap: var(--space-2);
    }

    #postTimeInput {
        padding: var(--space-2);
        padding-right: 2rem;
        font-size: 0.875rem;
        width: 100%;
    }

    #setCurrentTimeBtn {
        position: absolute;
        right: var(--space-1);
        top: 50%;
        transform: translateY(-50%);
        padding: var(--space-1);
    }

    /* 内容区域间距调整 */
    #postModal .px-6 {
        padding-left: var(--space-4);
        padding-right: var(--space-4);
    }

    #postModal .pt-0.pb-6 {
        padding-top: var(--space-2);
        padding-bottom: var(--space-4);
    }

    /* 文本区域优化 */
    #postModal textarea {
        padding: var(--space-4);
        min-height: 200px;
        font-size: 0.9rem;
    }

    /* 标签输入区域移动端优化 */
    #postModal .tags-input-wrapper {
        padding: var(--space-2);
        margin-top: var(--space-3);
    }

    #postModal .tags-input-wrapper .flex.items-center.gap-2 {
        flex-direction: row;
        align-items: center;
        gap: var(--space-2);
    }

    #postModal .tags-input-wrapper input {
        font-size: 0.875rem;
        padding: var(--space-2);
    }
}

    #postModal #modalTagInput {
        flex: 1;
        min-width: 0;
        font-size: 16px; /* 防止iOS自动缩放 */
        padding: var(--space-2);
    }

    /* 日期时间控件移动端优化 */
    #editTimeWrapper {
        overflow: hidden;
    }

    #editTimeWrapper .flex.items-center.justify-end {
        justify-content: flex-start;
        overflow: hidden;
    }

    #editTimeWrapper .flex.items-center.gap-2 {
        flex-wrap: nowrap;
        gap: var(--space-2);
        width: 100%;
        min-width: 0;
        overflow: hidden;
    }

    #postTimeInput {
        flex: 1;
        min-width: 0;
        max-width: 100%;
        font-size: var(--text-sm);
        width: auto;
        padding-right: 1.5rem;
    }

    #setCurrentTimeBtn {
        position: absolute;
        right: var(--space-1);
        top: 50%;
        transform: translateY(-50%);
        padding: var(--space-1);
        flex-shrink: 0;
    }

    /* 按钮尺寸调整 */
    .modal .close-button,
    .modal button[data-dismiss="modal"]:not(.custom-modal-button) {
        width: var(--fab-size-mobile);
        height: var(--fab-size-mobile);
        min-width: var(--fab-size-mobile);
        min-height: var(--fab-size-mobile);
        font-size: var(--text-xl);
    }
}

/* 小屏幕设备优化 (max-width: 480px) */
@media (max-width: 480px) {
    /* 模态框尺寸进一步调整 */
    .modal .bg-white {
        margin: var(--space-1);
        width: calc(100% - var(--space-2));
        min-width: 280px;
        max-width: calc(100% - var(--space-2));
        max-height: calc(100vh - var(--space-2));
    }

    /* 模态框头部进一步优化 */
    #postModal .flex.justify-between.items-center {
        padding: var(--space-2) var(--space-3);
    }

    #postModal #modalTitle {
        font-size: 0.9rem;
        padding: 0 var(--space-1);
    }

    /* 时间输入区域小屏幕优化 */
    #editTimeWrapper {
        padding: var(--space-2) var(--space-3);
    }

    #postTimeInput {
        padding: var(--space-2);
        font-size: 0.8rem;
    }

    /* 内容区域间距进一步减少 */
    #postModal .px-6 {
        padding-left: var(--space-3);
        padding-right: var(--space-3);
    }

    #postModal .pt-0.pb-6 {
        padding-top: var(--space-2);
        padding-bottom: var(--space-3);
    }

    /* 文本区域进一步优化 */
    #postModal textarea {
        padding: var(--space-3);
        min-height: 180px;
        font-size: 0.85rem;
    }

    /* 小屏幕设备标签输入区域优化 */
    #postModal .tags-input-wrapper {
        padding: var(--space-1) var(--space-2);
        margin-top: var(--space-2);
    }

    #postModal .tags-input-wrapper .flex.items-center.gap-2 {
        flex-direction: row;
        align-items: center;
        gap: var(--space-1);
    }

    #postModal .tags-input-wrapper input {
        font-size: 0.8rem;
        padding: var(--space-1) var(--space-2);
    }
}

    #postModal #modalTagInput {
        flex: 1;
        min-width: 0;
        padding: var(--space-1);
        font-size: 16px;
    }

    /* 小屏幕设备日期时间控件优化 */
    #editTimeWrapper {
        padding-left: var(--space-2);
        padding-right: var(--space-2);
        overflow: hidden;
    }

    #editTimeWrapper .flex.items-center.gap-2 {
        flex-wrap: nowrap;
        gap: var(--space-2);
        width: 100%;
        min-width: 0;
        overflow: hidden;
    }

    #postTimeInput {
        flex: 1;
        min-width: 0;
        max-width: 100%;
        font-size: var(--text-sm);
        width: auto;
        box-sizing: border-box;
        padding-right: 1.25rem;
    }

    #setCurrentTimeBtn {
        position: absolute;
        right: var(--space-1);
        top: 50%;
        transform: translateY(-50%);
        padding: var(--space-1);
        flex-shrink: 0;
    }
}