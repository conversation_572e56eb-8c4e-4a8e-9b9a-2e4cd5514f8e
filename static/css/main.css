/* 主样式入口文件 */

/* 引入设计令牌系统 */
@import url('./base/design-tokens.css');

/* 引入基础样式 */
@import url('./base/reset.css');

/* 引入组件样式 */
@import url('./components/base.css');
@import url('./components/modal.css');
@import url('./components/tags.css');
@import url('./components/markdown.css');

/* 引入工具类样式 */
@import url('./utilities/helpers.css');

/* 响应式优化 - 使用设计令牌系统 */
/* 注意：模态框的响应式样式已移至 components/modal.css 中统一管理 */

/* 标签输入框边框移除（保持向后兼容） */
#tagInput,
#editTagInput,
#modalTagInput {
    border: none;
    outline: none;
    box-shadow: none;
}
