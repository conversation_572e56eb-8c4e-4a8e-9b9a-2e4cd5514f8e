document.addEventListener('DOMContentLoaded', function () {

    /**
     * PostModalController
     * 统一管理新建和编辑笔记的模态框
     */
    class PostModalController {
        constructor(options) {
            this.options = options;
            this.modal = document.getElementById('postModal');
            this.form = document.getElementById('postForm');
            this.title = document.getElementById('modalTitle');
            this.submitButton = document.getElementById('submitButton');
            this.editor = document.getElementById('modalMarkdownEditor');
            this.postIdField = document.getElementById('postIdField');
            this.editTimeWrapper = document.getElementById('editTimeWrapper');
            this.postTimeInput = document.getElementById('postTimeInput');

            this.tagManager = new TagManager({
                tagInput: document.getElementById('modalTagInput'),
                tagsContainer: document.getElementById('modalTagsContainer'),
                tagsField: document.getElementById('modalTagsField')
            });

            this.bindEvents();
        }

        bindEvents() {
            // 关闭模态框
            this.modal.querySelectorAll('[data-dismiss="modal"]').forEach(el => {
                el.addEventListener('click', () => this.hide());
            });

            // 表单提交
            this.form.addEventListener('submit', (e) => this.handleSubmit(e));

            // 设置当前时间
            document.getElementById('setCurrentTimeBtn').addEventListener('click', () => this.setCurrentTime());
        }

        setCurrentTime() {
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            this.postTimeInput.value = `${year}-${month}-${day} ${hours}:${minutes}`;
        }

        resetForm() {
            this.form.reset();
            this.tagManager.clearTags(false);
            this.postIdField.value = '';
            this.editor.value = '';
        }

        show() {
            document.body.style.overflow = 'hidden';
            this.modal.classList.add('show');
        }

        hide() {
            document.body.style.overflow = '';
            this.modal.classList.remove('show');
            this.resetForm();
        }

        openForCreate() {
            this.resetForm();
            this.title.textContent = '发布笔记';
            this.editTimeWrapper.classList.add('hidden');
            this.show();
        }

        openForEdit(postId) {
            this.resetForm();
            fetch(`/api/post/${postId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const post = data.post;
                        this.title.textContent = '编辑笔记';
                        this.editor.value = post.content;
                        this.postIdField.value = post.id;
                        this.tagManager.setTags(post.tags || []);
                        
                        // 设置和显示发布时间
                        this.postTimeInput.value = post.created_at;
                        this.editTimeWrapper.classList.remove('hidden');

                        this.show();
                    } else {
                        alert('加载笔记失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('加载笔记失败，请重试');
                });
        }

        handleSubmit(e) {
            e.preventDefault();
            this.tagManager.processRemainingTag();

            // 检查用户设置的时间是否大于当前时间
            const userTime = this.postTimeInput.value;
            if (userTime) {
                const userDateTime = new Date(userTime);
                const currentDateTime = new Date();
                
                if (userDateTime > currentDateTime) {
                    const useCurrentTime = confirm('您设置的时间大于当前时间，是否使用当前时间？\n\n点击"确定"使用当前时间\n点击"取消"保持您设置的时间');
                    
                    if (useCurrentTime) {
                        this.setCurrentTime();
                    }
                }
            }

            const postId = this.postIdField.value;
            const isEditing = !!postId;
            const url = isEditing ? `/post/${postId}/update` : '/post/create';
            
            const formData = new FormData(this.form);
            // FormData 会自动处理 input 的值，但我们需要确保 tags 是 JSON 字符串
            formData.set('tags', JSON.stringify(this.tagManager.getTags()));
            
            // 手动添加发布时间字段（因为它在表单外部）
            if (this.postTimeInput.value) {
                formData.set('created_at', this.postTimeInput.value);
            }

            fetch(url, {
                method: 'POST',
                body: new URLSearchParams(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.hide();
                    if (isEditing) {
                        // 检查是否在随机页面
                        if (window.location.pathname === '/random') {
                            // 调用随机页面专用的更新函数
                            if (typeof window.updatePostInRandomDOM === 'function') {
                                window.updatePostInRandomDOM(data.post);
                            }
                        } else {
                            // 首页的更新函数
                            this.updatePostInDOM(data.post);
                        }
                    } else {
                        // 检查是否在随机页面
                        if (window.location.pathname === '/random') {
                            // 在随机页面发布新笔记后，更新页面显示新笔记
                            if (typeof window.updateRandomPostDisplay === 'function') {
                                // 更新随机页面的显示区域
                                const postDisplayArea = document.getElementById('post-display-area');
                                if (postDisplayArea) {
                                    // 创建新的笔记HTML结构
                                    const newPostHtml = `
                                        <div class="bg-white rounded-2xl p-8 shadow-lg border border-gray-200 relative overflow-hidden w-full max-w-2xl mx-auto animate-slide-in md:p-6" data-post-id="${data.post.id}">
                                            <div>
                                                <div class="flex justify-between items-center mb-6 pb-4 border-b border-gray-200">
                                                    <span class="text-sm text-gray-500 font-medium">${data.post.created_at}</span>
                                                    <div class="flex gap-2">
                                                        <button class="px-3 py-1 text-xs font-semibold border-0 rounded-xl cursor-pointer transition-all duration-300 bg-gray-100 text-gray-600 hover:bg-gray-200 hover:-translate-y-0.5 hover:shadow-sm edit-post" data-post-id="${data.post.id}">编辑</button>
                                                        <button class="px-3 py-1 text-xs font-semibold border-0 rounded-xl cursor-pointer transition-all duration-300 bg-red-100 text-red-600 hover:bg-red-200 hover:-translate-y-0.5 hover:shadow-sm delete-post" data-post-id="${data.post.id}">删除</button>
                                                    </div>
                                                </div>
                                                <div class="post-content leading-relaxed text-gray-800 mb-8 break-words markdown-content" id="content-${data.post.id}">${data.post.rendered_content}</div>
                                            </div>
                                            ${data.post.tags && data.post.tags.length > 0 ? 
                                            `<div class="tags-container mt-6 pt-4 border-t border-gray-200 flex flex-wrap gap-2">
                                                ${data.post.tags.map(tag => `
                                                    <a href="/?tag=${encodeURIComponent(tag)}"
                                                       class="note-tag note-tag--normal">
                                                        <span class="tag-text">${tag}</span>
                                                    </a>
                                                `).join('')}
                                            </div>` : ''}
                                        </div>
                                    `;
                                    postDisplayArea.innerHTML = newPostHtml;
                                }
                            }
                        } else {
                            window.location.reload(); // 首页新建后刷新页面以保证排序正确
                        }
                    }
                } else {
                    alert('操作失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('操作失败，请重试');
            });
        }

        updatePostInDOM(post) {
            const postCard = document.querySelector(`.card[data-post-id="${post.id}"]`);
            if (postCard) {
                // 更新内容
                const contentElement = postCard.querySelector('.post-content');
                if (contentElement) {
                    contentElement.innerHTML = post.rendered_content;
                }
                
                // 更新时间
                const timeElement = postCard.querySelector('.post-time');
                if (timeElement) {
                    timeElement.textContent = post.created_at;
                }
                
                // 更新标签容器
                const tagsContainer = postCard.querySelector('.tags-container');
                if (tagsContainer) {
                    tagsContainer.innerHTML = '';
                    if (post.tags && post.tags.length > 0) {
                        post.tags.forEach(tag => {
                            const tagLink = document.createElement('a');
                            tagLink.href = `/?tag=${encodeURIComponent(tag)}`;
                            tagLink.className = 'note-tag note-tag--normal';
                            tagLink.innerHTML = `<span class="tag-text">${tag}</span>`;
                            tagsContainer.appendChild(tagLink);
                        });
                    }
                }
                
                // 确保编辑和删除按钮仍然有正确的数据属性
                const editButton = postCard.querySelector('.edit-post');
                const deleteButton = postCard.querySelector('.delete-post');
                if (editButton) {
                    editButton.setAttribute('data-post-id', post.id);
                }
                if (deleteButton) {
                    deleteButton.setAttribute('data-post-id', post.id);
                }
            }
        }
    }

    

    /**
     * DeleteController
     * 管理删除确认模态框
     */
    class DeleteController {
        constructor() {
            this.modal = document.getElementById('deleteConfirmModal');
            this.confirmButton = this.modal.querySelector('.btn-danger');
            this.currentPostId = null;
            this.bindEvents();
        }

        bindEvents() {
            this.modal.querySelectorAll('[data-dismiss="modal"]').forEach(el => {
                el.addEventListener('click', () => this.hide());
            });
            this.confirmButton.addEventListener('click', (e) => this.handleDelete(e));
        }

        show(postId) {
            this.currentPostId = postId;
            this.modal.classList.add('show');
        }

        hide() {
            this.currentPostId = null;
            this.modal.classList.remove('show');
        }

        handleDelete(e) {
            e.preventDefault();
            if (!this.currentPostId) return;

            fetch(`/post/${this.currentPostId}/delete`, { method: 'POST' })
                .then(response => {
                    if (response.ok) {
                        document.querySelector(`.card[data-post-id="${this.currentPostId}"]`).remove();
                        this.hide();
                    } else {
                        throw new Error('删除失败');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('删除失败，请重试');
                    this.hide();
                });
        }
    }

    // 初始化所有控制器
    const postModalController = new PostModalController();
    const deleteController = new DeleteController();

    // 暴露全局函数供随机页面调用
    window.openPostModal = function() {
        postModalController.openForCreate();
    };

    // 暴露全局函数供随机页面更新笔记显示
    window.updateRandomPostDisplay = function(postData) {
        postModalController.updatePostInDOM(postData);
    };

    // 全局事件委托
    document.addEventListener('click', function (event) {
        const target = event.target;

        // 新建笔记
        if (target.closest('#postButton')) {
            postModalController.openForCreate();
        }

        // 编辑笔记
        if (target.classList.contains('edit-post')) {
            const postId = target.getAttribute('data-post-id');
            postModalController.openForEdit(postId);
        }

        // 删除笔记
        if (target.classList.contains('delete-post')) {
            const postId = target.getAttribute('data-post-id');
            deleteController.show(postId);
        }
    });
});
