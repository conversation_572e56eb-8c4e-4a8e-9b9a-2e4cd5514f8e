document.addEventListener('DOMContentLoaded', function () {
    const postDisplayArea = document.getElementById('post-display-area');
    const randomAgainButton = document.getElementById('random-again-btn');
    
    // 删除笔记相关元素
    const deleteModal = document.getElementById('deleteConfirmModal');
    const deleteForm = document.getElementById('deletePostForm');
    
    // 显示删除确认模态框
    function showDeleteModal() {
        deleteModal.classList.add('show');
    }
    
    // 隐藏删除确认模态框
    function hideDeleteModal() {
        deleteModal.classList.remove('show');
    }
    
    function updatePostDisplay(postData) {
        if (!postData) {
            postDisplayArea.innerHTML = `
                <div class="bg-white rounded-2xl p-8 shadow-lg border border-gray-200 relative overflow-hidden w-full max-w-2xl mx-auto animate-slide-in">
                    <div class="text-base leading-relaxed text-gray-500 text-center italic">加载笔记失败或没有笔记。</div>
                </div>`;
            return;
        }

        let tagsHtml = '';
        if (postData.tags && postData.tags.length > 0) {
            // 生成标签的 HTML，使用统一的 note-tag 样式
            postData.tags.forEach(tag => {
                tagsHtml += `
                    <a href="/?tag=${encodeURIComponent(tag)}"
                       class="note-tag note-tag--normal">
                        <span class="tag-text">${escapeHtml(tag)}</span>
                    </a>`;
            });
        } else {
            tagsHtml = `
                <span class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-400 bg-gray-100 rounded-full border border-gray-200">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    无标签
                </span>`;
        }


        postDisplayArea.innerHTML = `
            <div class="bg-white rounded-2xl p-8 shadow-lg border border-gray-200 relative overflow-hidden w-full max-w-2xl mx-auto animate-slide-in md:p-6" data-post-id="${postData.id}">
                 <div>
                    <div class="flex justify-between items-center mb-6 pb-4 border-b border-gray-200 md:flex-col md:items-start md:gap-2">
                        <span class="text-sm text-gray-500 font-medium md:order-2 md:text-xs">${postData.created_at}</span>
                        <div class="flex gap-2 md:order-1 md:w-full md:justify-end">
                            <button class="px-3 py-1 text-xs font-semibold border-0 rounded-xl cursor-pointer transition-all duration-300 bg-gray-100 text-gray-600 hover:bg-gray-200 hover:-translate-y-0.5 hover:shadow-sm edit-post" data-post-id="${postData.id}">编辑</button>
                            <button class="px-3 py-1 text-xs font-semibold border-0 rounded-xl cursor-pointer transition-all duration-300 bg-red-100 text-red-600 hover:bg-red-200 hover:-translate-y-0.5 hover:shadow-sm delete-post" data-post-id="${postData.id}">删除</button>
                        </div>
                    </div>
                    <div class="text-base leading-relaxed text-gray-800 mb-8 break-words markdown-content md:text-sm" id="content-${postData.id}">${postData.rendered_content || escapeHtml(postData.content)}</div>
                 </div>
                 <div class="flex flex-wrap gap-2 mt-6 pt-4 border-t border-gray-200">
                    ${tagsHtml}
                </div>
            </div>`;
    }
    
    // 更新随机页面上的笔记显示（用于编辑后更新）
    function updatePostInRandomDOM(post) {
        const postCard = document.querySelector(`[data-post-id="${post.id}"]`);
        if (postCard) {
            // 更新内容
            const contentElement = postCard.querySelector('.markdown-content');
            if (contentElement) {
                contentElement.innerHTML = post.rendered_content;
            }
            
            // 更新时间
            const timeElement = postCard.querySelector('.text-gray-500');
            if (timeElement) {
                timeElement.textContent = post.created_at;
            }
            
            // 更新标签容器
            const tagsContainer = postCard.querySelector('.flex.flex-wrap.gap-2');
            if (tagsContainer) {
                // 重新生成标签HTML，使用统一的 note-tag 样式
                let tagsHtml = '';
                if (post.tags && post.tags.length > 0) {
                    post.tags.forEach(tag => {
                        tagsHtml += `
                            <a href="/?tag=${encodeURIComponent(tag)}"
                               class="note-tag note-tag--normal">
                                <span class="tag-text">${escapeHtml(tag)}</span>
                            </a>`;
                    });
                } else {
                    tagsHtml = `
                        <span class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-400 bg-gray-100 rounded-full border border-gray-200">
                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            无标签
                        </span>`;
                }
                tagsContainer.innerHTML = tagsHtml;
            }
            
            // 确保编辑和删除按钮仍然有正确的数据属性
            const editButton = postCard.querySelector('.edit-post');
            const deleteButton = postCard.querySelector('.delete-post');
            if (editButton) {
                editButton.setAttribute('data-post-id', post.id);
            }
            if (deleteButton) {
                deleteButton.setAttribute('data-post-id', post.id);
            }
        }
    }

    // 防止 XSS
    function escapeHtml(unsafe) {
        if (unsafe === null || typeof unsafe === 'undefined') {
            return '';
        }
        return unsafe
             .replace(/&/g, "&amp;")
             .replace(/</g, "&lt;")
             .replace(/>/g, "&gt;")
             .replace(/"/g, "&quot;")
             .replace(/'/g, "&#039;");
    }

    function fetchPost(url) {
        fetch(url)
            .then(response => {
                if (!response.ok) {
                    // 尝试解析错误信息
                    return response.json().then(err => { throw new Error(err.message || `HTTP error ${response.status}`) });
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    updatePostDisplay(data.post);
                } else {
                    console.error('API Error:', data.message);
                    alert('加载笔记失败: ' + data.message);
                    // 可以选择显示错误信息，或者禁用按钮等
                     updatePostDisplay(null); // 显示错误状态
                }
            })
            .catch(error => {
                console.error('Fetch Error:', error);
                alert('网络错误或服务器无法响应: ' + error.message);
                 updatePostDisplay(null); // 显示错误状态
            });
    }

    // 再来一条按钮事件
    randomAgainButton.addEventListener('click', function () {
        fetchPost('/api/random_post'); // 调用获取随机笔记的 API
    });
    
    // 删除确认模态框关闭事件
    deleteModal.querySelectorAll('[data-dismiss="modal"]').forEach(button => {
        button.addEventListener('click', hideDeleteModal);
    });
    
    // 点击删除模态框背景时关闭
    deleteModal.addEventListener('click', function (event) {
        if (event.target === deleteModal) {
            hideDeleteModal();
        }
    });
    
    // 使用事件委托处理删除按钮点击
    document.addEventListener('click', function (event) {
        const target = event.target;
        
        // 删除笔记处理
        if (target.classList.contains('delete-post')) {
            const postId = target.getAttribute('data-post-id');
            showDeleteModal();
            
            const confirmDeleteBtn = deleteModal.querySelector('button[type="submit"]');
            confirmDeleteBtn.onclick = function (e) {
                e.preventDefault();
                fetch(`/post/${postId}/delete`, {
                    method: 'POST'
                })
                .then(response => {
                    if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
                    hideDeleteModal();
                    // 删除成功后获取新的随机笔记
                    fetchPost('/api/random_post');
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('删除笔记失败，请重试');
                    hideDeleteModal();
                });
            };
        }
    });
    
    // 暴露 updatePostInRandomDOM 函数给全局作用域，以便 main.js 可以调用
    window.updatePostInRandomDOM = updatePostInRandomDOM;

});
