<!-- 通用笔记模态框 (新建/编辑) -->
<div class="modal fixed z-50 left-0 top-0 w-full h-full bg-black/70 backdrop-blur-sm animate-fade-in" id="postModal">
    <div class="bg-white mx-auto rounded-2xl shadow-2xl border border-gray-300 animate-slide-in overflow-hidden flex flex-col">
        <!-- 模态框头部 -->
        <div class="flex justify-between items-center px-6 py-3 bg-gradient-to-br from-indigo-50/50 to-purple-50/50 flex-shrink-0">
            <button type="button" class="p-2 text-gray-500 hover:text-gray-700 transition-colors duration-200" data-dismiss="modal" title="取消">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
            <h5 class="text-lg font-semibold text-gray-800 m-0 absolute left-1/2 transform -translate-x-1/2 px-4" id="modalTitle">发布笔记</h5>
            <button type="submit" form="postForm" id="submitButton" class="p-2 text-indigo-600 hover:text-indigo-800 transition-colors duration-200" title="保存">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
            </button>
        </div>
        
        <!-- 发布时间设置 (仅编辑时显示) -->
        <div id="editTimeWrapper" class="hidden bg-gray-50 rounded-lg px-6 py-2 border-b border-gray-200">
            <div class="flex items-center justify-end">
                <div class="flex items-center gap-2 flex-nowrap">
                    <div class="relative flex-1 min-w-0">
                        <input type="text" id="postTimeInput" name="created_at" placeholder="例如: 2024-01-15 14:30"
                               autocomplete="off"
                               class="w-full px-2 py-1 text-sm border border-gray-300 rounded-md transition-all duration-200 bg-white text-gray-600 focus:outline-none focus:border-indigo-500 focus:ring-1 focus:ring-indigo-100 pr-8">
                        <button type="button" id="setCurrentTimeBtn" class="absolute right-1 top-1/2 transform -translate-y-1/2 p-1 text-indigo-600 hover:text-indigo-800 transition-colors duration-200" title="设置为当前时间">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 模态框内容 -->
        <div class="px-6 pt-0 pb-6 flex-1 overflow-y-auto">
            <form id="postForm">
                <!-- 隐藏的笔记ID字段 -->
                <input type="hidden" id="postIdField" name="post_id">

                <!-- Markdown 编辑器 -->
                <div class="flex flex-col gap-0 rounded-xl border-2 border-gray-200 overflow-hidden focus-within:border-indigo-500 focus-within:shadow-sm focus-within:shadow-indigo-100 transition-all duration-300">
                    <div class="editor-container">
                        <textarea name="content" id="modalMarkdownEditor" placeholder="记录你的想法和灵感..." required
                                  class="w-full min-h-250 md:min-h-300 p-5 text-base border-none font-mono resize-y bg-white focus:outline-none"></textarea>
                    </div>
                </div>
                
                <!-- 标签编辑区域 -->
                <div class="flex flex-col gap-4">
                    <div class="flex-1 min-w-0">
                        <div class="tags-input-wrapper tags-component bg-gray-50 rounded-lg px-3 py-2">
                            <div class="flex flex-wrap gap-2 w-full" id="modalTagsContainer">
                                <!-- 标签将在这里动态添加 -->
                                <div class="flex items-center gap-2 flex-wrap flex-1 min-w-0">
                                    <input type="text" id="modalTagInput" placeholder="添加标签..."
                                           class="px-2 py-1 text-sm rounded-md transition-all duration-200 bg-white text-gray-600 focus:outline-none flex-1 min-w-0"
                                           enterkeyhint="done" inputmode="text" />
                                </div>
                            </div>
                        </div>
                        <input type="hidden" name="tags" id="modalTagsField" value="[]" />
                    </div>
                </div>
            </form>
        </div>

        
    </div>
</div>
