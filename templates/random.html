<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>随机笔记</title>
    <link rel="icon" href="/static/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="/static/favicon.ico" type="image/x-icon">
    <!-- 引入第三方库样式 -->
    <link href="/static/css/vendors/tailwind.min.css" rel="stylesheet">
    <!-- 引入主样式文件 -->
    <link rel="stylesheet" href="/static/css/main.css">
    <!-- 引入悬浮按钮组件样式 -->
    <link rel="stylesheet" href="/static/css/components/floating-buttons.css">
</head>
<body>
    <!-- 固定导航栏 -->
    {% set active_page = 'random' %}
    {% include '_navbar.html' %}

    <!-- 主容器 -->
    <div class="max-w-4xl mx-auto px-8 py-8 mt-20 md:px-4 md:mt-16">
        <div id="post-display-area" class="mb-12">
            {% if post %}
            <div class="bg-white rounded-2xl p-8 shadow-lg border border-gray-200 relative overflow-hidden w-full max-w-2xl mx-auto animate-slide-in md:p-6" data-post-id="{{ post.id }}">
                 <div>
                    <div class="flex justify-between items-center mb-6 pb-4 border-b border-gray-200">
                        <span class="text-sm text-gray-500 font-medium">{{ post.created_at.strftime('%Y-%m-%d %H:%M') }}</span>
                        <div class="flex gap-2">
                            <button class="px-3 py-1 text-xs font-semibold border-0 rounded-xl cursor-pointer transition-all duration-300 bg-gray-100 text-gray-600 hover:bg-gray-200 hover:-translate-y-0.5 hover:shadow-sm edit-post" data-post-id="{{ post.id }}">编辑</button>
                            <button class="px-3 py-1 text-xs font-semibold border-0 rounded-xl cursor-pointer transition-all duration-300 bg-red-100 text-red-600 hover:bg-red-200 hover:-translate-y-0.5 hover:shadow-sm delete-post" data-post-id="{{ post.id }}">删除</button>
                        </div>
                    </div>
                    <div class="post-content leading-relaxed text-gray-800 mb-8 break-words markdown-content" id="content-{{ post.id }}">{{ post.rendered_content|safe }}</div>
                </div>
                {% if post.tags %}
                <div class="tags-container mt-6 pt-4 border-t border-gray-200 flex flex-wrap gap-2">
                    {% for tag in post.tags or [] %} {# 添加 or [] 防止 tags 为 None #}
                    <a href="{{ url_for('index', tag=tag) }}"
                       class="note-tag note-tag--normal">
                        <span class="tag-text">{{ tag }}</span>
                    </a>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
            {% else %}
            <div class="bg-white rounded-2xl p-8 shadow-lg border border-gray-200 relative overflow-hidden w-full max-w-2xl mx-auto animate-slide-in">
                <div class="text-base leading-relaxed text-gray-500 text-center italic">没有找到任何笔记。</div>
            </div>
            {% endif %}
        </div>


    </div>


    <!-- 引入通用模态框 -->
    {% include '_post_modal.html' %}

    <!-- 删除确认对话框 -->
    <div class="modal fixed z-50 left-0 top-0 w-full h-full bg-black/50 backdrop-blur-sm animate-fade-in" id="deleteConfirmModal">
        <div class="bg-white my-[10%] mx-auto rounded-2xl w-[95%] max-w-lg shadow-2xl animate-slide-in overflow-hidden">
            <div class="flex justify-between items-center px-8 py-6 bg-gradient-to-br from-indigo-50/50 to-purple-50/50 border-b border-gray-200">
                <h5 class="text-lg font-semibold text-gray-800 m-0">确认删除</h5>
                <button type="button" class="bg-none border-0 text-2xl text-gray-500 cursor-pointer p-0 w-8 h-8 flex items-center justify-center rounded-full transition-all duration-300 hover:bg-red-50 hover:text-red-500" data-dismiss="modal">&times;</button>
            </div>
            <div class="modal-body p-8">
                <p class="text-gray-700 text-base">确定要删除这条微博吗？</p>
            </div>
            <div class="modal-footer px-8 py-6 bg-gray-50 border-t border-gray-200 flex justify-end gap-4">
                <button type="button" class="px-4 py-2 text-sm font-semibold bg-indigo-600 text-white border-0 rounded-xl cursor-pointer transition-all duration-300 hover:bg-indigo-700" data-dismiss="modal">取消</button>
                <form id="deletePostForm" method="POST" class="inline">
                    <button type="submit" class="btn-danger px-4 py-2 text-sm font-semibold bg-red-500 text-white border-0 rounded-xl cursor-pointer transition-all duration-300 hover:bg-red-600">删除</button>
                </form>
            </div>
        </div>
    </div>

    <!-- 悬浮按钮组 - 右侧垂直布局 -->
    <!-- 随机按钮 -->
    <button id="random-again-btn"
            class="floating-action-button floating-action-button--bottom-right-upper floating-action-button--success floating-action-button--animate-in"
            title="随机一条笔记">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
        </svg>
    </button>
    
    <!-- 发布按钮 -->
    <button id="postButton"
            class="floating-action-button floating-action-button--bottom-right floating-action-button--primary floating-action-button--animate-in"
            title="发布新笔记">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
        </svg>
    </button>

    <script>
        window.API_URLS = {
            createPost: '{{ url_for("create_post") }}',
            updatePost: '{{ url_for("update_post", post_id=0) }}'
        };
        
        // 发布按钮点击事件 - 直接在当前页面打开发布模态框
        document.addEventListener('DOMContentLoaded', function() {
            const postButton = document.getElementById('postButton');
            if (postButton) {
                postButton.addEventListener('click', function() {
                    // 直接调用全局函数打开发布模态框
                    if (typeof window.openPostModal === 'function') {
                        window.openPostModal();
                    }
                });
            }
        });
    </script>
    <script src="/static/js/search.js"></script>
    <script src="/static/js/tags.js"></script>
    <script src="/static/js/random.js"></script>
    <script src="/static/js/main.js"></script>
    <script src="/static/js/navbar.js"></script>
</body>
</html>
