<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模态框响应式测试</title>
    <link rel="stylesheet" href="static/css/main.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #4f46e5;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .test-button:hover {
            background: #4338ca;
        }
        .info-panel {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 8px;
            padding: 16px;
            margin: 20px 0;
        }
        .screen-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="screen-info" id="screenInfo">
        屏幕尺寸: <span id="screenSize"></span>
    </div>
    
    <div class="test-container">
        <h1>笔记模态框响应式测试</h1>
        
        <div class="info-panel">
            <h3>测试说明</h3>
            <p>请在不同的窗口尺寸下测试模态框的表现：</p>
            <ul>
                <li><strong>桌面端</strong>: > 1200px</li>
                <li><strong>平板端</strong>: 768px - 1200px</li>
                <li><strong>移动端</strong>: < 768px</li>
            </ul>
        </div>
        
        <button class="test-button" onclick="openModal()">打开笔记模态框</button>
        <button class="test-button" onclick="openEditModal()">打开编辑模态框</button>
    </div>

    <!-- 笔记模态框 -->
    <div class="modal fixed z-50 left-0 top-0 w-full h-full bg-black/70 backdrop-blur-sm animate-fade-in" id="postModal">
        <div class="bg-white mx-auto rounded-2xl shadow-2xl border border-gray-300 animate-slide-in overflow-hidden flex flex-col">
            <!-- 模态框头部 -->
            <div class="flex justify-between items-center px-6 py-3 bg-gradient-to-br from-indigo-50/50 to-purple-50/50 flex-shrink-0">
                <button type="button" class="p-2 text-gray-500 hover:text-gray-700 transition-colors duration-200" onclick="closeModal()" title="取消">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
                <h5 class="text-lg font-semibold text-gray-800 m-0 absolute left-1/2 transform -translate-x-1/2 px-4" id="modalTitle">发布笔记</h5>
                <button type="button" class="p-2 text-indigo-600 hover:text-indigo-800 transition-colors duration-200" title="保存">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                </button>
            </div>
            
            <!-- 发布时间设置 -->
            <div id="editTimeWrapper" class="hidden bg-gray-50 rounded-lg px-6 py-2 border-b border-gray-200">
                <div class="flex items-center justify-end">
                    <div class="flex items-center gap-2 flex-nowrap">
                        <div class="relative flex-1 min-w-0">
                            <input type="text" id="postTimeInput" placeholder="例如: 2024-01-15 14:30"
                                   class="w-full px-2 py-1 text-sm border border-gray-300 rounded-md bg-white text-gray-600 pr-8">
                            <button type="button" class="absolute right-1 top-1/2 transform -translate-y-1/2 p-1 text-indigo-600">
                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6l4 2"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 模态框内容 -->
            <div class="px-6 pt-0 pb-6 flex-1 overflow-y-auto">
                <!-- Markdown 编辑器 -->
                <div class="flex flex-col gap-0 rounded-xl border-2 border-gray-200 overflow-hidden focus-within:border-indigo-500 focus-within:shadow-sm focus-within:shadow-indigo-100 transition-all duration-300">
                    <div class="editor-container">
                        <textarea id="modalMarkdownEditor" placeholder="记录你的想法和灵感..." 
                                  class="w-full min-h-250 md:min-h-300 p-5 text-base border-none font-mono resize-y bg-white focus:outline-none"></textarea>
                    </div>
                </div>
                
                <!-- 标签编辑区域 -->
                <div class="flex flex-col gap-4 mt-4">
                    <div class="flex-1 min-w-0">
                        <div class="tags-input-wrapper tags-component bg-gray-50 rounded-lg px-3 py-2">
                            <div class="flex flex-wrap gap-2 w-full">
                                <div class="flex items-center gap-2 flex-wrap flex-1 min-w-0">
                                    <input type="text" placeholder="添加标签..."
                                           class="px-2 py-1 text-sm rounded-md bg-white text-gray-600 focus:outline-none flex-1 min-w-0" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 显示屏幕尺寸信息
        function updateScreenInfo() {
            const width = window.innerWidth;
            const height = window.innerHeight;
            let category = '';
            
            if (width > 1200) {
                category = '桌面端';
            } else if (width >= 768) {
                category = '平板端';
            } else {
                category = '移动端';
            }
            
            document.getElementById('screenSize').textContent = `${width}x${height} (${category})`;
        }
        
        // 打开模态框
        function openModal() {
            document.getElementById('postModal').classList.add('show');
            document.getElementById('editTimeWrapper').classList.add('hidden');
            document.getElementById('modalTitle').textContent = '发布笔记';
        }
        
        // 打开编辑模态框
        function openEditModal() {
            document.getElementById('postModal').classList.add('show');
            document.getElementById('editTimeWrapper').classList.remove('hidden');
            document.getElementById('modalTitle').textContent = '编辑笔记';
            document.getElementById('postTimeInput').value = '2024-01-15 14:30';
        }
        
        // 关闭模态框
        function closeModal() {
            document.getElementById('postModal').classList.remove('show');
        }
        
        // 初始化
        updateScreenInfo();
        window.addEventListener('resize', updateScreenInfo);
        
        // 点击背景关闭模态框
        document.getElementById('postModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
    </script>
</body>
</html>
